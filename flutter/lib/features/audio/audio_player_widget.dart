import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';

/// Виджет для отображения времени аудио в формате MM:SS
class AudioTimeText extends StatelessWidget {
  final Duration duration;

  const AudioTimeText({super.key, required this.duration});

  @override
  Widget build(BuildContext context) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    String timeString;
    if (hours > 0) {
      timeString =
          '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      timeString =
          '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }

    return Text(
      timeString,
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
      ),
    );
  }
}

/// Виджет аудиоплеера для воспроизведения аудиофайлов
class AudioPlayerWidget extends StatelessWidget {
  /// Текущая позиция воспроизведения
  final Duration position;

  /// Общая длительность аудиофайла
  final Duration duration;

  /// Состояние воспроизведения (играет/пауза)
  final bool isPlaying;

  /// Callback для переключения воспроизведения/паузы
  final VoidCallback onPlayPause;

  /// Callback для перемотки назад
  final Function(int seconds) onSeekBackward;

  /// Callback для перемотки вперед
  final Function(int seconds) onSeekForward;

  /// Callback для остановки воспроизведения
  final VoidCallback onStop;

  /// Callback для перемотки по слайдеру
  final Function(int seconds) onSeek;

  const AudioPlayerWidget({
    super.key,
    required this.position,
    required this.duration,
    this.isPlaying = false,
    required this.onPlayPause,
    required this.onSeekBackward,
    required this.onSeekForward,
    required this.onStop,
    required this.onSeek,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final backgroundColor = colorScheme.surface;
    final textColor = colorScheme.onSurface;

    // Убран виджет Positioned, так как позиционирование должно управляться
    // родительским виджетом (Stack в wiki_content.dart)
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withAlpha(33),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Полоска для обозначения возможности свайпа вниз
          Padding(
            padding: const EdgeInsets.only(top: 10),
            child: Container(
              width: 36,
              height: 5,
              decoration: BoxDecoration(
                color: colorScheme.onSurfaceVariant.withAlpha(51),
                borderRadius: BorderRadius.circular(2.5),
              ),
            ),
          ),

          // Верхняя панель с временем и кнопкой закрытия
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Время
                Row(
                  children: [
                    AudioTimeText(duration: position),
                    Text(
                      ' / ',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: textColor.withAlpha(153),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    AudioTimeText(duration: duration),
                  ],
                ),
                // Кнопка закрытия
                IconButton(
                  icon: Icon(
                    LucideIcons.x,
                    color: textColor.withAlpha(179),
                    size: 18,
                  ),
                  onPressed: onStop,
                  tooltip: 'Закрыть',
                  style: IconButton.styleFrom(
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    minimumSize: const Size(36, 36),
                    padding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ),

          // Слайдер для перемотки
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
            child: Slider(
              min: 0,
              max: duration.inSeconds > 0 ? duration.inSeconds.toDouble() : 1.0,
              value:
                  duration.inSeconds > 0
                      ? position.inSeconds
                          .clamp(0, duration.inSeconds)
                          .toDouble()
                      : 0.0,
              onChanged:
                  duration.inSeconds > 0
                      ? (newValue) {
                        onSeek(newValue.round());
                      }
                      : null,
              activeColor: textColor,
              inactiveColor: textColor.withAlpha(60),
            ),
          ),

          // Кнопки управления
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Кнопка назад на 10 секунд
                _ControlButton(
                  icon: Icons.replay_10,
                  onPressed: () => onSeekBackward(10),
                  color: textColor,
                ),

                // Кнопка воспроизведения/паузы
                _PlayButton(
                  isPlaying: isPlaying,
                  onPlayPause: onPlayPause,
                  color: textColor,
                ),

                // Кнопка вперед на 10 секунд
                _ControlButton(
                  icon: Icons.forward_10,
                  onPressed: () => onSeekForward(10),
                  color: textColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Кнопка управления
class _ControlButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onPressed;
  final Color color;

  const _ControlButton({
    required this.icon,
    required this.onPressed,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        customBorder: const CircleBorder(),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Icon(icon, color: color, size: 32),
        ),
      ),
    );
  }
}

/// Кнопка воспроизведения/паузы
class _PlayButton extends StatelessWidget {
  final bool isPlaying;
  final VoidCallback onPlayPause;
  final Color color;

  const _PlayButton({
    required this.isPlaying,
    required this.onPlayPause,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color.withAlpha(26),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPlayPause,
          customBorder: const CircleBorder(),
          child: Center(
            child: Padding(
              // Подгоняем позицию иконки с учетом ее геометрии
              padding: EdgeInsets.only(
                left:
                    isPlaying
                        ? 0
                        : 3, // Сдвиг для иконки Play, чтобы она центрировалась
              ),
              child: Icon(
                isPlaying ? Icons.pause : Icons.play_arrow,
                color: color,
                size: 32,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
